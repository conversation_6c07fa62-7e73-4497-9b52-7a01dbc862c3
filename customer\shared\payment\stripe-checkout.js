// Stripe Checkout JavaScript

// Get Stripe publishable key and client secret
const publishableKey = document.getElementById('publishableKey').value;
const clientSecret = document.getElementById('clientSecret').value;

// Initialize Stripe with reduced error reporting
const stripe = Stripe(publishableKey, {
    stripeAccount: undefined,
    apiVersion: undefined,
    locale: 'en'
});
let elements;

// DOM elements
const paymentForm = document.getElementById('payment-form');
const submitButton = document.getElementById('submit-button');
const buttonText = document.getElementById('button-text');
const spinner = document.getElementById('spinner');
const paymentMessage = document.getElementById('payment-message');

// Order summary elements
const summaryBillboardType = document.getElementById('summaryBillboardType');
const summaryDates = document.getElementById('summaryDates');
const summaryDuration = document.getElementById('summaryDuration');
const summaryAmount = document.getElementById('summaryAmount');

// Customer info elements
const customerNameInput = document.getElementById('customerName');
const customerEmailInput = document.getElementById('customerEmail');

// Load order data from localStorage
function loadOrderData() {
    // Billboard type
    const billboardType = localStorage.getItem('billboardType') || 'Custom';
    summaryBillboardType.textContent = billboardType.charAt(0).toUpperCase() + billboardType.slice(1);
    
    // Selected dates
    const selectedDatesStr = localStorage.getItem('selectedBillboardDates');
    if (selectedDatesStr) {
        try {
            const selectedDates = JSON.parse(selectedDatesStr);
            if (selectedDates.length > 0) {
                const startDate = new Date(selectedDates[0]);
                const endDate = new Date(selectedDates[selectedDates.length - 1]);
                
                const formatDate = (date) => {
                    return date.toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric', 
                        year: 'numeric' 
                    });
                };
                
                if (selectedDates.length === 1) {
                    summaryDates.textContent = formatDate(startDate);
                } else {
                    summaryDates.textContent = `${formatDate(startDate)} - ${formatDate(endDate)}`;
                }
                
                summaryDuration.textContent = `${selectedDates.length} day${selectedDates.length !== 1 ? 's' : ''}`;
            }
        } catch (e) {
            console.error('Error parsing selected dates:', e);
            summaryDates.textContent = 'Error loading dates';
        }
    } else {
        summaryDates.textContent = 'No dates selected';
    }
    
    // Customer info
    const customerName = localStorage.getItem('customerName');
    const customerEmail = localStorage.getItem('customerEmail');
    
    if (customerName) {
        customerNameInput.value = customerName;
    }
    
    if (customerEmail) {
        customerEmailInput.value = customerEmail;
    }
}

// Initialize Stripe Elements
async function initializeStripe() {
    const appearance = {
        theme: 'stripe',
        variables: {
            colorPrimary: '#28a745',
            colorBackground: '#ffffff',
            colorText: '#2c3e50',
            colorDanger: '#dc3545',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            spacingUnit: '4px',
            borderRadius: '8px'
        }
    };
    
    elements = stripe.elements({ clientSecret, appearance });
    
    const paymentElement = elements.create('payment');
    paymentElement.mount('#payment-element');
    
    // Fetch payment intent to get amount
    try {
        const { paymentIntent } = await stripe.retrievePaymentIntent(clientSecret);
        if (paymentIntent) {
            const amount = (paymentIntent.amount / 100).toFixed(2);
            summaryAmount.textContent = `$${amount}`;
        }
    } catch (error) {
        console.error('Error retrieving payment intent:', error);
    }
}

// Show payment message
function showMessage(messageText, type = 'error') {
    paymentMessage.textContent = messageText;
    paymentMessage.classList.remove('error', 'success');
    paymentMessage.classList.add(type);
    paymentMessage.style.display = 'block';
}

// Set loading state
function setLoading(isLoading) {
    if (isLoading) {
        submitButton.disabled = true;
        spinner.style.display = 'inline-block';
        buttonText.textContent = 'Processing...';
    } else {
        submitButton.disabled = false;
        spinner.style.display = 'none';
        buttonText.textContent = 'Complete Payment';
    }
}

// Handle form submission
async function handleSubmit(e) {
    e.preventDefault();
    
    // Validate customer info
    const customerName = customerNameInput.value.trim();
    const customerEmail = customerEmailInput.value.trim();
    
    if (!customerName) {
        showMessage('Please enter your full name');
        customerNameInput.focus();
        return;
    }
    
    if (!customerEmail || !isValidEmail(customerEmail)) {
        showMessage('Please enter a valid email address');
        customerEmailInput.focus();
        return;
    }
    
    setLoading(true);
    
    try {
        // Save customer info to localStorage
        localStorage.setItem('customerName', customerName);
        localStorage.setItem('customerEmail', customerEmail);
        
        // Extract payment intent ID from client secret
        const clientSecret = new URLSearchParams(window.location.search).get('client_secret') || '';
        const paymentIntentId = clientSecret.split('_secret_')[0];

        // Create customer in Stripe with better error handling
        console.log('Creating customer with data:', {
            payment_intent_id: paymentIntentId,
            fullname: customerName,
            email: customerEmail
        });

        const customerResponse = await fetch('create-customer.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                payment_intent_id: paymentIntentId,
                fullname: customerName,
                email: customerEmail
            })
        });

        console.log('Customer creation response status:', customerResponse.status);

        if (!customerResponse.ok) {
            const errorText = await customerResponse.text();
            console.error('Customer creation failed:', errorText);
            throw new Error(`Failed to create customer (HTTP ${customerResponse.status}): ${errorText}`);
        }

        const customerResponseText = await customerResponse.text();
        console.log('Customer creation response text:', customerResponseText);

        let customerResult;
        try {
            customerResult = JSON.parse(customerResponseText);
        } catch (parseError) {
            console.error('Failed to parse customer response:', customerResponseText);
            throw new Error(`Invalid JSON response from customer creation: ${customerResponseText}`);
        }

        if (!customerResult.success) {
            console.error('Customer creation failed:', customerResult);
            throw new Error(customerResult.error || 'Failed to create customer');
        }

        console.log('Customer created successfully:', customerResult);

        // Completely disable refresh protection before payment confirmation to prevent redirect warnings
        console.log('🔄 Disabling refresh protection for payment completion');

        // Use the specialized payment completion method
        if (typeof RefreshProtection !== 'undefined' && RefreshProtection.disableForPaymentCompletion) {
            RefreshProtection.disableForPaymentCompletion();
        } else {
            // Fallback methods
            if (typeof RefreshProtection !== 'undefined' && RefreshProtection.disableAll) {
                RefreshProtection.disableAll();
            }

            if (window.refreshProtectionInstance) {
                window.refreshProtectionInstance.disable();
                window.refreshProtectionInstance = null;
            }

            // Remove all beforeunload listeners to prevent warnings
            window.onbeforeunload = null;

            // Set global flags
            window.paymentInProgress = true;
            window.paymentCompleted = true;
        }

        // Confirm payment
        const { error } = await stripe.confirmPayment({
            elements,
            confirmParams: {
                return_url: `${window.location.origin}/customer/shared/payment/payment-success.php?customer_id=${customerResult.customer_id}&source=stripe_redirect`
            }
        });
        
        // This point will only be reached if there is an immediate error when
        // confirming the payment. Otherwise, the customer will be redirected to
        // the return_url.
        if (error.type === 'card_error' || error.type === 'validation_error') {
            showMessage(error.message);
        } else {
            showMessage('An unexpected error occurred.');
        }
        
    } catch (error) {
        console.error('Payment error:', error);
        showMessage(error.message || 'An error occurred during payment processing');
    }
    
    setLoading(false);
}

// Validate email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Suppress Stripe error reporting to reduce console noise
const originalConsoleError = console.error;
console.error = function(...args) {
    const message = args.join(' ');
    // Filter out Stripe Sentry errors and rate limiting
    if (message.includes('errors.stripe.com') ||
        message.includes('sentry') ||
        message.includes('429') ||
        message.includes('Too Many Requests')) {
        return; // Don't log these errors
    }
    originalConsoleError.apply(console, args);
};

// Also suppress network errors from Stripe's error reporting
window.addEventListener('error', function(e) {
    if (e.filename && e.filename.includes('stripe.com')) {
        e.preventDefault();
        return false;
    }
});

// Suppress unhandled promise rejections from Stripe
window.addEventListener('unhandledrejection', function(e) {
    if (e.reason && e.reason.message &&
        (e.reason.message.includes('stripe.com') ||
         e.reason.message.includes('sentry') ||
         e.reason.message.includes('429'))) {
        e.preventDefault();
        return false;
    }
});

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadOrderData();
    initializeStripe();

    // Add form submit handler
    paymentForm.addEventListener('submit', handleSubmit);
});

// Check for payment status on page load
window.addEventListener('load', async function() {
    // Check if we have a payment_intent_client_secret in the URL
    const params = new URLSearchParams(window.location.search);
    const clientSecret = params.get('client_secret');
    
    if (clientSecret) {
        try {
            const { paymentIntent } = await stripe.retrievePaymentIntent(clientSecret);
            
            if (paymentIntent) {
                switch (paymentIntent.status) {
                    case 'succeeded':
                        showMessage('Payment succeeded! Redirecting to confirmation page...', 'success');

                        // Completely disable all protection before redirect
                        console.log('✅ Payment succeeded - disabling all protection for redirect');

                        // Use the specialized payment completion method
                        if (typeof RefreshProtection !== 'undefined' && RefreshProtection.disableForPaymentCompletion) {
                            RefreshProtection.disableForPaymentCompletion();
                        } else {
                            // Fallback methods
                            if (typeof RefreshProtection !== 'undefined' && RefreshProtection.disableAll) {
                                RefreshProtection.disableAll();
                            }

                            if (window.refreshProtectionInstance) {
                                window.refreshProtectionInstance.disable();
                                window.refreshProtectionInstance = null;
                            }

                            // Remove all beforeunload listeners
                            window.onbeforeunload = null;

                            // Set global flags
                            window.paymentCompleted = true;
                        }

                        setTimeout(() => {
                            // Direct redirect without any protection interference
                            window.location.href = 'payment-success.php?tid=' + paymentIntent.id;
                        }, 1000);
                        break;
                    case 'processing':
                        showMessage('Your payment is processing. We\'ll update you when payment is received.', 'success');
                        break;
                    case 'requires_payment_method':
                        showMessage('Please provide your payment information to complete the purchase.');
                        break;
                    default:
                        showMessage('Something went wrong with your payment. Please try again.');
                        break;
                }
            }
        } catch (error) {
            console.error('Error checking payment status:', error);
        }
    }
});
