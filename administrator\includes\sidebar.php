<?php
/**
 * Reusable Admin Sidebar Component
 * Supports collapsing/expanding functionality for better UX
 */

// Get current page for active state management
$current_page = basename($_SERVER['PHP_SELF']);

// Define navigation items
$nav_items = [
    [
        'url' => 'orders.php',
        'label' => 'List of Orders',
        'icon' => 'fas fa-list-alt',
        'page' => 'orders.php'
    ],
    [
        'url' => 'financial-reports.php',
        'label' => 'Financial Dashboard',
        'icon' => 'fas fa-chart-line',
        'page' => 'financial-reports.php'
    ],
    [
        'url' => 'history.php',
        'label' => 'History',
        'icon' => 'fas fa-history',
        'page' => 'history.php'
    ],
    [
        'url' => '?logout=1',
        'label' => 'Logout',
        'icon' => 'fas fa-sign-out-alt',
        'class' => 'logout-btn',
        'page' => ''
    ]
];

// Check if sidebar should be collapsed based on cookie
$isCollapsed = isset($_COOKIE['sidebarCollapsed']) && $_COOKIE['sidebarCollapsed'] === 'true';
?>

<!-- Sidebar -->
<aside class="admin-sidebar <?php echo $isCollapsed ? 'collapsed' : ''; ?>" id="adminSidebar">
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle" aria-label="Toggle Sidebar" type="button">
        <i class="fas <?php echo $isCollapsed ? 'fa-chevron-right' : 'fa-bars'; ?>"></i>
    </button>
    
    <!-- Navigation -->
    <nav class="sidebar-nav">
        <ul class="nav-list">
            <?php foreach ($nav_items as $item): ?>
                <li class="nav-item">
                    <a href="<?php echo htmlspecialchars($item['url']); ?>" 
                       class="nav-link <?php echo ($current_page === $item['page']) ? 'active' : ''; ?> <?php echo isset($item['class']) ? $item['class'] : ''; ?>"
                       <?php if (isset($item['class']) && $item['class'] === 'logout-btn'): ?>
                           onclick="return confirm('Are you sure you want to logout?')"
                       <?php endif; ?>>
                        <i class="<?php echo htmlspecialchars($item['icon']); ?>"></i>
                        <span class="nav-text"><?php echo htmlspecialchars($item['label']); ?></span>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
    </nav>
    
    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="user-info">
            <i class="fas fa-user-circle"></i>
            <span class="user-name"><?php echo htmlspecialchars($current_admin['username']); ?></span>
        </div>
    </div>
</aside>

<!-- Sidebar Overlay for Mobile -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<script>
// Simple, reliable sidebar toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('adminSidebar');
    const toggleBtn = document.getElementById('sidebarToggle');
    const overlay = document.getElementById('sidebarOverlay');
    
    if (!sidebar || !toggleBtn) {
        console.error('Sidebar elements not found');
        return;
    }
    
    // Initialize from localStorage
    const savedState = localStorage.getItem('sidebarCollapsed');
    const isCollapsed = savedState === 'true';
    
    // Apply initial state
    if (isCollapsed) {
        sidebar.classList.add('collapsed');
        document.body.classList.add('sidebar-collapsed');
        updateIcon(true);
    } else {
        sidebar.classList.remove('collapsed');
        document.body.classList.remove('sidebar-collapsed');
        updateIcon(false);
    }
    
    // Update toggle button icon
    function updateIcon(collapsed) {
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            icon.className = collapsed ? 'fas fa-chevron-right' : 'fas fa-bars';
        }
    }
    
    // Toggle function
    function toggleSidebar() {
        const isCurrentlyCollapsed = sidebar.classList.contains('collapsed');
        const newState = !isCurrentlyCollapsed;
        
        // Update classes
        if (newState) {
            sidebar.classList.add('collapsed');
            document.body.classList.add('sidebar-collapsed');
        } else {
            sidebar.classList.remove('collapsed');
            document.body.classList.remove('sidebar-collapsed');
        }
        
        // Save state
        localStorage.setItem('sidebarCollapsed', newState.toString());
        document.cookie = 'sidebarCollapsed=' + newState + '; path=/; max-age=31536000';
        
        // Update icon
        updateIcon(newState);
        
        console.log('Sidebar toggled:', newState ? 'collapsed' : 'expanded');
    }
    
    // Event listeners
    toggleBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleSidebar();
    });
    
    // Mobile overlay
    if (overlay) {
        overlay.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                sidebar.classList.add('collapsed');
                document.body.classList.add('sidebar-collapsed');
                localStorage.setItem('sidebarCollapsed', 'true');
                document.cookie = 'sidebarCollapsed=true; path=/; max-age=31536000';
                updateIcon(true);
            }
        });
    }
    
    console.log('Sidebar initialized:', isCollapsed ? 'collapsed' : 'expanded');
});
</script>
